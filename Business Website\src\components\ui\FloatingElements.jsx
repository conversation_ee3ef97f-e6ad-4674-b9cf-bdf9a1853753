import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../context/ThemeContext';
import { 
  ArrowUp, 
  MessageCircle, 
  Phone, 
  Mail,
  X,
  ChevronUp
} from 'lucide-react';

// Scroll to top button
export const ScrollToTop = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { isDark } = useTheme();

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.button
          initial={{ opacity: 0, scale: 0, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0, y: 20 }}
          whileHover={{ 
            scale: 1.1,
            boxShadow: isDark 
              ? '0 10px 30px rgba(96, 165, 250, 0.4)'
              : '0 10px 30px rgba(59, 130, 246, 0.3)'
          }}
          whileTap={{ scale: 0.95 }}
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 z-50 p-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
          style={{
            background: isDark 
              ? 'linear-gradient(135deg, #60a5fa, #a78bfa)'
              : 'linear-gradient(135deg, #3b82f6, #8b5cf6)'
          }}
        >
          <ChevronUp size={24} />
        </motion.button>
      )}
    </AnimatePresence>
  );
};

// Floating contact buttons
export const FloatingContact = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { isDark } = useTheme();

  const contactOptions = [
    {
      icon: Phone,
      label: 'Call Us',
      href: 'tel:+1234567890',
      color: 'from-green-500 to-emerald-600',
      delay: 0.1
    },
    {
      icon: Mail,
      label: 'Email Us',
      href: 'mailto:<EMAIL>',
      color: 'from-red-500 to-pink-600',
      delay: 0.2
    },
    {
      icon: MessageCircle,
      label: 'Live Chat',
      href: '#',
      color: 'from-blue-500 to-cyan-600',
      delay: 0.3
    }
  ];

  return (
    <div className="fixed bottom-6 left-6 z-50">
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute bottom-16 left-0 space-y-3"
          >
            {contactOptions.map((option, index) => (
              <motion.a
                key={option.label}
                href={option.href}
                initial={{ opacity: 0, x: -20, scale: 0.8 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: -20, scale: 0.8 }}
                transition={{ delay: option.delay }}
                whileHover={{ 
                  scale: 1.1,
                  x: 5,
                  boxShadow: isDark 
                    ? '0 10px 30px rgba(96, 165, 250, 0.3)'
                    : '0 10px 30px rgba(59, 130, 246, 0.2)'
                }}
                whileTap={{ scale: 0.95 }}
                className={`flex items-center space-x-3 p-3 bg-gradient-to-r ${option.color} text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 group`}
              >
                <option.icon size={20} />
                <span className="text-sm font-medium pr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  {option.label}
                </span>
              </motion.a>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      <motion.button
        whileHover={{ 
          scale: 1.1,
          boxShadow: isDark 
            ? '0 10px 30px rgba(96, 165, 250, 0.4)'
            : '0 10px 30px rgba(59, 130, 246, 0.3)'
        }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsOpen(!isOpen)}
        className="p-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
        style={{
          background: isDark 
            ? 'linear-gradient(135deg, #60a5fa, #a78bfa)'
            : 'linear-gradient(135deg, #3b82f6, #8b5cf6)'
        }}
      >
        <motion.div
          animate={{ rotate: isOpen ? 45 : 0 }}
          transition={{ duration: 0.2 }}
        >
          {isOpen ? <X size={24} /> : <MessageCircle size={24} />}
        </motion.div>
      </motion.button>
    </div>
  );
};

// Animated cursor follower (for desktop)
export const CursorFollower = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const { isDark } = useTheme();

  useEffect(() => {
    const updateMousePosition = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    const handleMouseEnter = () => setIsHovering(true);
    const handleMouseLeave = () => setIsHovering(false);

    // Add event listeners to interactive elements
    const interactiveElements = document.querySelectorAll('a, button, [role="button"]');
    
    interactiveElements.forEach(el => {
      el.addEventListener('mouseenter', handleMouseEnter);
      el.addEventListener('mouseleave', handleMouseLeave);
    });

    window.addEventListener('mousemove', updateMousePosition);

    return () => {
      window.removeEventListener('mousemove', updateMousePosition);
      interactiveElements.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter);
        el.removeEventListener('mouseleave', handleMouseLeave);
      });
    };
  }, []);

  // Only show on desktop
  if (window.innerWidth < 768) return null;

  return (
    <motion.div
      className="fixed top-0 left-0 pointer-events-none z-50 mix-blend-difference"
      animate={{
        x: mousePosition.x - 10,
        y: mousePosition.y - 10,
        scale: isHovering ? 1.5 : 1,
      }}
      transition={{
        type: "spring",
        stiffness: 500,
        damping: 28,
        mass: 0.5
      }}
    >
      <div
        className="w-5 h-5 rounded-full"
        style={{
          background: isDark 
            ? 'radial-gradient(circle, rgba(96, 165, 250, 0.8) 0%, rgba(96, 165, 250, 0.2) 70%, transparent 100%)'
            : 'radial-gradient(circle, rgba(59, 130, 246, 0.8) 0%, rgba(59, 130, 246, 0.2) 70%, transparent 100%)',
          boxShadow: isDark 
            ? '0 0 20px rgba(96, 165, 250, 0.6)'
            : '0 0 20px rgba(59, 130, 246, 0.4)'
        }}
      />
    </motion.div>
  );
};

// Loading animation
export const PageLoader = ({ isLoading }) => {
  const { isDark } = useTheme();

  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-slate-900"
        >
          <div className="text-center">
            <motion.div
              className="w-16 h-16 border-4 border-gray-200 dark:border-gray-700 border-t-blue-600 rounded-full mx-auto mb-4"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-gray-600 dark:text-gray-300 font-medium"
            >
              Loading...
            </motion.p>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
