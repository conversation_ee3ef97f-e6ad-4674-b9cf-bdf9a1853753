import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../context/ThemeContext';

const AnimatedCard = ({ 
  children, 
  className = '', 
  delay = 0,
  hoverEffect = 'lift',
  glowOnHover = false,
  index = 0,
  ...props 
}) => {
  const { isDark } = useTheme();

  const hoverEffects = {
    lift: {
      y: -8,
      scale: 1.02,
      transition: { duration: 0.3, ease: "easeOut" }
    },
    scale: {
      scale: 1.05,
      transition: { duration: 0.3, ease: "easeOut" }
    },
    rotate: {
      rotateY: 5,
      scale: 1.02,
      transition: { duration: 0.3, ease: "easeOut" }
    },
    glow: {
      boxShadow: isDark 
        ? '0 20px 40px rgba(96, 165, 250, 0.3), 0 0 0 1px rgba(96, 165, 250, 0.2)'
        : '0 20px 40px rgba(59, 130, 246, 0.2), 0 0 0 1px rgba(59, 130, 246, 0.1)',
      transition: { duration: 0.3, ease: "easeOut" }
    }
  };

  const cardVariants = {
    hidden: { 
      opacity: 0, 
      y: 50,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        delay: delay + (index * 0.1),
        ease: "easeOut"
      }
    }
  };

  const glowStyles = glowOnHover ? {
    boxShadow: isDark 
      ? '0 4px 20px rgba(96, 165, 250, 0.1)'
      : '0 4px 20px rgba(59, 130, 246, 0.08)',
  } : {};

  return (
    <motion.div
      className={`relative overflow-hidden ${className}`}
      variants={cardVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-50px" }}
      whileHover={hoverEffects[hoverEffect]}
      style={glowStyles}
      {...props}
    >
      {/* Animated border gradient */}
      <motion.div
        className="absolute inset-0 rounded-inherit"
        style={{
          background: isDark 
            ? 'linear-gradient(45deg, rgba(96, 165, 250, 0.1), rgba(168, 85, 247, 0.1), rgba(236, 72, 153, 0.1))'
            : 'linear-gradient(45deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05), rgba(219, 39, 119, 0.05))',
          opacity: 0
        }}
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      />

      {/* Shine effect on hover */}
      <motion.div
        className="absolute inset-0 rounded-inherit"
        style={{
          background: isDark
            ? 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%)'
            : 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.6) 50%, transparent 70%)',
          transform: 'translateX(-100%)'
        }}
        whileHover={{
          transform: 'translateX(100%)',
          transition: { duration: 0.6, ease: "easeInOut" }
        }}
      />

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.div>
  );
};

// Specialized card variants
export const ServiceCard = ({ icon: Icon, title, description, features, price, className = '', ...props }) => {
  const { isDark } = useTheme();
  
  return (
    <AnimatedCard
      className={`bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 ${className}`}
      hoverEffect="lift"
      glowOnHover={true}
      {...props}
    >
      <motion.div
        className="flex flex-col h-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        {/* Icon */}
        <motion.div
          className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mb-6"
          whileHover={{ 
            scale: 1.1, 
            rotate: 5,
            boxShadow: isDark 
              ? '0 10px 30px rgba(96, 165, 250, 0.4)'
              : '0 10px 30px rgba(59, 130, 246, 0.3)'
          }}
          transition={{ duration: 0.3 }}
        >
          <Icon className="text-white" size={24} />
        </motion.div>

        {/* Content */}
        <motion.h3 
          className="text-xl font-semibold text-gray-900 dark:text-white mb-4"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          {title}
        </motion.h3>
        
        <motion.p 
          className="text-gray-600 dark:text-gray-300 mb-6 flex-grow"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          {description}
        </motion.p>

        {/* Features */}
        {features && (
          <motion.ul 
            className="space-y-2 mb-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            {features.map((feature, index) => (
              <motion.li
                key={index}
                className="flex items-center text-sm text-gray-600 dark:text-gray-300"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 + (index * 0.1) }}
              >
                <motion.div
                  className="w-2 h-2 bg-green-500 rounded-full mr-3"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.7 + (index * 0.1), type: "spring" }}
                />
                {feature}
              </motion.li>
            ))}
          </motion.ul>
        )}

        {/* Price */}
        {price && (
          <motion.div 
            className="border-t border-gray-200 dark:border-gray-600 pt-6"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <div className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {price}
            </div>
          </motion.div>
        )}
      </motion.div>
    </AnimatedCard>
  );
};

export const TeamCard = ({ image, name, role, bio, social, className = '', ...props }) => {
  return (
    <AnimatedCard
      className={`bg-white dark:bg-slate-800 rounded-2xl p-6 shadow-lg text-center ${className}`}
      hoverEffect="lift"
      glowOnHover={true}
      {...props}
    >
      <motion.div
        className="relative mb-6"
        whileHover={{ scale: 1.05 }}
        transition={{ duration: 0.3 }}
      >
        <img
          src={image}
          alt={name}
          className="w-24 h-24 rounded-full mx-auto object-cover"
        />
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full opacity-0"
          whileHover={{ opacity: 0.2 }}
          transition={{ duration: 0.3 }}
        />
      </motion.div>
      
      <motion.h3 
        className="text-xl font-semibold text-gray-900 dark:text-white mb-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        {name}
      </motion.h3>
      
      <motion.p 
        className="text-blue-600 dark:text-blue-400 font-medium mb-3"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        {role}
      </motion.p>
      
      <motion.p 
        className="text-gray-600 dark:text-gray-300 text-sm mb-4"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        {bio}
      </motion.p>
      
      {social && (
        <motion.div 
          className="flex justify-center space-x-3"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          {Object.entries(social).map(([platform, url], index) => (
            <motion.a
              key={platform}
              href={url}
              className="text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              whileHover={{ scale: 1.2, y: -2 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6 + (index * 0.1), type: "spring" }}
            >
              {/* Icon would go here based on platform */}
            </motion.a>
          ))}
        </motion.div>
      )}
    </AnimatedCard>
  );
};

export default AnimatedCard;
