import{c as createShadow}from"../shared/create-shadow.min.mjs";import{e as effectInit}from"../shared/effect-init.min.mjs";import{e as effectTarget}from"../shared/effect-target.min.mjs";import{e as effectVirtualTransitionEnd}from"../shared/effect-virtual-transition-end.min.mjs";import{g as getSlideTransformEl,p as getRotateFix}from"../shared/utils.min.mjs";function EffectCreative(e){let{swiper:t,extendParams:s,on:r}=e;s({creativeEffect:{limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});const a=e=>"string"==typeof e?e:`${e}px`;effectInit({effect:"creative",swiper:t,on:r,setTranslate:()=>{const{slides:e,wrapperEl:s,slidesSizesGrid:r}=t,i=t.params.creativeEffect,{progressMultiplier:o}=i,l=t.params.centeredSlides,n=getRotateFix(t);if(l){const e=r[0]/2-t.params.slidesOffsetBefore||0;s.style.transform=`translateX(calc(50% - ${e}px))`}for(let s=0;s<e.length;s+=1){const r=e[s],c=r.progress,f=Math.min(Math.max(r.progress,-i.limitProgress),i.limitProgress);let m=f;l||(m=Math.min(Math.max(r.originalProgress,-i.limitProgress),i.limitProgress));const p=r.swiperSlideOffset,d=[t.params.cssMode?-p-t.translate:-p,0,0],g=[0,0,0];let h=!1;t.isHorizontal()||(d[1]=d[0],d[0]=0);let w={translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1};f<0?(w=i.next,h=!0):f>0&&(w=i.prev,h=!0),d.forEach(((e,t)=>{d[t]=`calc(${e}px + (${a(w.translate[t])} * ${Math.abs(f*o)}))`})),g.forEach(((e,t)=>{let s=w.rotate[t]*Math.abs(f*o);g[t]=s})),r.style.zIndex=-Math.abs(Math.round(c))+e.length;const y=d.join(", "),u=`rotateX(${n(g[0])}deg) rotateY(${n(g[1])}deg) rotateZ(${n(g[2])}deg)`,v=m<0?`scale(${1+(1-w.scale)*m*o})`:`scale(${1-(1-w.scale)*m*o})`,E=m<0?1+(1-w.opacity)*m*o:1-(1-w.opacity)*m*o,M=`translate3d(${y}) ${u} ${v}`;if(h&&w.shadow||!h){let e=r.querySelector(".swiper-slide-shadow");if(!e&&w.shadow&&(e=createShadow("creative",r)),e){const t=i.shadowPerProgress?f*(1/i.limitProgress):f;e.style.opacity=Math.min(Math.max(Math.abs(t),0),1)}}const $=effectTarget(i,r);$.style.transform=M,$.style.opacity=E,w.origin&&($.style.transformOrigin=w.origin)}},setTransition:e=>{const s=t.slides.map((e=>getSlideTransformEl(e)));s.forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow").forEach((t=>{t.style.transitionDuration=`${e}ms`}))})),effectVirtualTransitionEnd({swiper:t,duration:e,transformElements:s,allSlides:!0})},perspective:()=>t.params.creativeEffect.perspective,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!t.params.cssMode})})}export{EffectCreative as default};
//# sourceMappingURL=effect-creative.min.mjs.map