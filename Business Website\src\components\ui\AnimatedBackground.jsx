import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../context/ThemeContext';

const AnimatedBackground = ({ children, className = '', variant = 'default' }) => {
  const { isDark } = useTheme();

  const backgroundVariants = {
    default: {
      light: {
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        particles: '#3b82f6',
        particleOpacity: 0.1
      },
      dark: {
        background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
        particles: '#60a5fa',
        particleOpacity: 0.2
      }
    },
    hero: {
      light: {
        background: 'linear-gradient(135deg, #dbeafe 0%, #f3e8ff 50%, #fce7f3 100%)',
        particles: '#3b82f6',
        particleOpacity: 0.15
      },
      dark: {
        background: 'linear-gradient(135deg, #0f172a 0%, #1e1b4b 50%, #581c87 100%)',
        particles: '#a78bfa',
        particleOpacity: 0.25
      }
    },
    section: {
      light: {
        background: 'linear-gradient(45deg, #ffffff 0%, #f8fafc 100%)',
        particles: '#6366f1',
        particleOpacity: 0.08
      },
      dark: {
        background: 'linear-gradient(45deg, #1e293b 0%, #334155 100%)',
        particles: '#818cf8',
        particleOpacity: 0.15
      }
    }
  };

  const currentTheme = isDark ? 'dark' : 'light';
  const currentVariant = backgroundVariants[variant][currentTheme];

  const particleCount = 20;
  const particles = Array.from({ length: particleCount }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    size: Math.random() * 4 + 1,
    duration: Math.random() * 20 + 10,
    delay: Math.random() * 5
  }));

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Animated Background */}
      <motion.div
        className="absolute inset-0"
        initial={{ opacity: 0 }}
        animate={{ 
          opacity: 1,
          background: currentVariant.background
        }}
        transition={{ duration: 0.8 }}
        style={{ background: currentVariant.background }}
      />

      {/* Floating Particles */}
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: currentVariant.particles,
            opacity: currentVariant.particleOpacity,
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, Math.random() * 20 - 10, 0],
            scale: [1, 1.2, 1],
            opacity: [currentVariant.particleOpacity, currentVariant.particleOpacity * 0.5, currentVariant.particleOpacity],
          }}
          transition={{
            duration: particle.duration,
            delay: particle.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}

      {/* Geometric Shapes */}
      <motion.div
        className="absolute top-10 right-10 w-32 h-32 rounded-full opacity-10"
        style={{ backgroundColor: currentVariant.particles }}
        animate={{
          scale: [1, 1.1, 1],
          rotate: [0, 180, 360],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear",
        }}
      />

      <motion.div
        className="absolute bottom-20 left-20 w-24 h-24 opacity-10"
        style={{ 
          backgroundColor: currentVariant.particles,
          clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)'
        }}
        animate={{
          scale: [1, 1.2, 1],
          rotate: [0, -180, -360],
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: "linear",
        }}
      />

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default AnimatedBackground;
