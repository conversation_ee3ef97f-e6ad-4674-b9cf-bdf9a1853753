import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  Code,
  Smartphone,
  TrendingUp,
  Shield,
  Palette,
  Database,
  Cloud,
  Search,
  ArrowRight,
  Check,
  Star,
  Zap,
  Globe,
  Users
} from 'lucide-react';

const Services = () => {
  const services = [
    {
      icon: Code,
      title: 'Web Development',
      description: 'Modern, responsive websites and web applications that drive results.',
      features: ['React & Next.js', 'Responsive Design', 'SEO Optimized', 'Lightning Fast'],
      color: 'from-blue-500 to-cyan-500',
      price: 'Starting at $2,999',
      popular: false
    },
    {
      icon: Smartphone,
      title: 'Mobile App Development',
      description: 'Native iOS and Android apps that provide exceptional user experiences.',
      features: ['Native Performance', 'Cross-Platform', 'App Store Ready', 'Push Notifications'],
      color: 'from-purple-500 to-pink-500',
      price: 'Starting at $4,999',
      popular: true
    },
    {
      icon: TrendingUp,
      title: 'Digital Marketing',
      description: 'Data-driven marketing strategies that grow your business exponentially.',
      features: ['SEO & SEM', 'Social Media', 'Content Marketing', 'Analytics & Reporting'],
      color: 'from-green-500 to-emerald-500',
      price: 'Starting at $1,499/month',
      popular: false
    },
    {
      icon: Palette,
      title: 'UI/UX Design',
      description: 'Beautiful and intuitive user interfaces that enhance user experience.',
      features: ['User Research', 'Wireframing', 'Prototyping', 'Design Systems'],
      color: 'from-orange-500 to-red-500',
      price: 'Starting at $1,999',
      popular: false
    },
    {
      icon: Database,
      title: 'Backend Development',
      description: 'Robust and scalable backend solutions for your applications.',
      features: ['API Development', 'Database Design', 'Cloud Integration', 'Security'],
      color: 'from-indigo-500 to-purple-500',
      price: 'Starting at $3,499',
      popular: false
    },
    {
      icon: Cloud,
      title: 'Cloud Solutions',
      description: 'Cloud infrastructure and deployment solutions for modern applications.',
      features: ['AWS/Azure/GCP', 'DevOps', 'Scalability', 'Monitoring'],
      color: 'from-teal-500 to-blue-500',
      price: 'Starting at $999/month',
      popular: false
    }
  ];

  const process = [
    {
      step: '01',
      title: 'Discovery',
      description: 'We understand your business goals and requirements.'
    },
    {
      step: '02',
      title: 'Strategy',
      description: 'We develop a comprehensive strategy tailored to your needs.'
    },
    {
      step: '03',
      title: 'Design',
      description: 'We create beautiful and functional designs.'
    },
    {
      step: '04',
      title: 'Development',
      description: 'We build your solution using best practices.'
    },
    {
      step: '05',
      title: 'Testing',
      description: 'We thoroughly test everything before launch.'
    },
    {
      step: '06',
      title: 'Launch',
      description: 'We deploy your solution and provide ongoing support.'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Modern Hero Section */}
      <section className="relative py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10" />
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl" />
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-pink-500/20 rounded-full blur-3xl" />
        </div>
        
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-5xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-purple-500/20 border border-purple-500/30 mb-8"
            >
              <Star className="text-purple-400" size={16} />
              <span className="text-purple-300 font-medium">Premium Digital Services</span>
            </motion.div>

            <h1 className="text-6xl md:text-8xl font-bold text-white mb-8 leading-tight">
              Transform Your
              <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
                Digital Vision
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed mb-12 max-w-4xl mx-auto">
              From cutting-edge web development to AI-powered solutions, we deliver 
              exceptional digital experiences that drive growth and innovation.
            </p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Link
                to="/contact"
                className="px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full font-semibold text-lg hover:scale-105 transition-transform duration-300 hover:shadow-2xl hover:shadow-purple-500/25"
              >
                Start Your Project
              </Link>
              <Link
                to="/portfolio"
                className="px-8 py-4 border-2 border-purple-400 text-purple-300 rounded-full font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300"
              >
                View Our Work
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Modern Services Grid */}
      <section className="py-32 bg-gray-50 dark:bg-slate-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <h2 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Our <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Services</span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Each service is crafted with precision and delivered with excellence. 
              From concept to completion, we ensure every project exceeds expectations.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ 
                  y: -10,
                  scale: 1.02,
                  boxShadow: "0 25px 50px rgba(147, 51, 234, 0.15)"
                }}
                className={`group relative bg-white dark:bg-slate-800 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600 h-full flex flex-col ${
                  service.popular ? 'ring-2 ring-purple-500 ring-offset-2 ring-offset-gray-50 dark:ring-offset-slate-900' : ''
                }`}
              >
                {service.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </div>
                  </div>
                )}

                <motion.div
                  className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${service.color} rounded-2xl mb-6 group-hover:shadow-lg`}
                  whileHover={{
                    scale: 1.1,
                    rotate: 5,
                    boxShadow: "0 15px 35px rgba(147, 51, 234, 0.4)"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <service.icon className="text-white" size={28} />
                </motion.div>

                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                  {service.title}
                </h3>

                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed flex-grow font-medium">
                  {service.description}
                </p>

                <ul className="space-y-3 mb-8">
                  {service.features.map((feature, idx) => (
                    <motion.li
                      key={idx}
                      className="flex items-center text-gray-700 dark:text-gray-200 font-medium"
                      initial={{ opacity: 0, x: -10 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * idx }}
                    >
                      <Check className="text-purple-500 mr-3 flex-shrink-0" size={18} />
                      {feature}
                    </motion.li>
                  ))}
                </ul>

                <div className="border-t border-gray-200 dark:border-slate-600 pt-6 mt-auto">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-6">
                    {service.price}
                  </div>
                  <Link
                    to="/contact"
                    className="w-full inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-semibold hover:scale-105 transition-transform duration-300 hover:shadow-lg group"
                  >
                    Get Started
                    <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={18} />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Services;
