import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../context/ThemeContext';

const AnimatedText = ({ 
  children, 
  variant = 'fadeInUp', 
  delay = 0, 
  duration = 0.6,
  className = '',
  as = 'div',
  stagger = false,
  glowEffect = false
}) => {
  const { isDark } = useTheme();
  const Component = motion[as];

  const variants = {
    fadeInUp: {
      hidden: { opacity: 0, y: 30 },
      visible: { opacity: 1, y: 0 }
    },
    fadeInDown: {
      hidden: { opacity: 0, y: -30 },
      visible: { opacity: 1, y: 0 }
    },
    fadeInLeft: {
      hidden: { opacity: 0, x: -30 },
      visible: { opacity: 1, x: 0 }
    },
    fadeInRight: {
      hidden: { opacity: 0, x: 30 },
      visible: { opacity: 1, x: 0 }
    },
    scaleIn: {
      hidden: { opacity: 0, scale: 0.8 },
      visible: { opacity: 1, scale: 1 }
    },
    slideInUp: {
      hidden: { opacity: 0, y: 50, scale: 0.95 },
      visible: { opacity: 1, y: 0, scale: 1 }
    },
    typewriter: {
      hidden: { width: 0 },
      visible: { width: 'auto' }
    }
  };

  const glowStyles = glowEffect ? {
    textShadow: isDark 
      ? '0 0 20px rgba(96, 165, 250, 0.5), 0 0 40px rgba(96, 165, 250, 0.3)' 
      : '0 0 20px rgba(59, 130, 246, 0.3), 0 0 40px rgba(59, 130, 246, 0.1)',
    filter: isDark ? 'brightness(1.1)' : 'brightness(0.95)'
  } : {};

  if (stagger && typeof children === 'string') {
    const words = children.split(' ');
    return (
      <Component
        className={className}
        initial="hidden"
        animate="visible"
        style={glowStyles}
        transition={{
          staggerChildren: 0.1,
          delayChildren: delay
        }}
      >
        {words.map((word, index) => (
          <motion.span
            key={index}
            variants={variants[variant]}
            transition={{ duration, ease: "easeOut" }}
            className="inline-block mr-2"
          >
            {word}
          </motion.span>
        ))}
      </Component>
    );
  }

  return (
    <Component
      className={className}
      variants={variants[variant]}
      initial="hidden"
      animate="visible"
      transition={{ 
        duration, 
        delay, 
        ease: "easeOut",
        type: variant === 'scaleIn' ? 'spring' : 'tween',
        stiffness: variant === 'scaleIn' ? 100 : undefined
      }}
      style={glowStyles}
    >
      {children}
    </Component>
  );
};

// Specialized components for common use cases
export const AnimatedHeading = ({ children, level = 1, className = '', ...props }) => {
  const HeadingTag = `h${level}`;
  return (
    <AnimatedText
      as={HeadingTag}
      className={`font-bold ${className}`}
      glowEffect={true}
      {...props}
    >
      {children}
    </AnimatedText>
  );
};

export const AnimatedParagraph = ({ children, className = '', ...props }) => {
  return (
    <AnimatedText
      as="p"
      className={`text-gray-600 dark:text-gray-300 ${className}`}
      {...props}
    >
      {children}
    </AnimatedText>
  );
};

export const TypewriterText = ({ text, className = '', speed = 100 }) => {
  const { isDark } = useTheme();
  
  return (
    <motion.div
      className={`overflow-hidden whitespace-nowrap border-r-2 ${
        isDark ? 'border-blue-400' : 'border-blue-600'
      } ${className}`}
      initial={{ width: 0 }}
      animate={{ width: 'auto' }}
      transition={{
        duration: text.length * speed / 1000,
        ease: "linear"
      }}
    >
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        {text}
      </motion.span>
      <motion.span
        animate={{ opacity: [1, 0] }}
        transition={{
          duration: 0.8,
          repeat: Infinity,
          repeatType: "reverse"
        }}
        className={`ml-1 ${isDark ? 'text-blue-400' : 'text-blue-600'}`}
      >
        |
      </motion.span>
    </motion.div>
  );
};

export default AnimatedText;
