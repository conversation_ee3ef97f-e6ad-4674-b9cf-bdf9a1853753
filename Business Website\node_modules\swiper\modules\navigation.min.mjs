import{c as createElementIfNotDefined}from"../shared/create-element-if-not-defined.min.mjs";import{m as makeElementsArray}from"../shared/utils.min.mjs";function Navigation(a){let{swiper:e,extendParams:n,on:i,emit:t}=a;function s(a){let n;return a&&"string"==typeof a&&e.isElement&&(n=e.el.querySelector(a)||e.hostEl.querySelector(a),n)?n:(a&&("string"==typeof a&&(n=[...document.querySelectorAll(a)]),e.params.uniqueNavElements&&"string"==typeof a&&n&&n.length>1&&1===e.el.querySelectorAll(a).length?n=e.el.querySelector(a):n&&1===n.length&&(n=n[0])),a&&!n?a:n)}function l(a,n){const i=e.params.navigation;(a=makeElementsArray(a)).forEach((a=>{a&&(a.classList[n?"add":"remove"](...i.disabledClass.split(" ")),"BUTTON"===a.tagName&&(a.disabled=n),e.params.watchOverflow&&e.enabled&&a.classList[e.isLocked?"add":"remove"](i.lockClass))}))}function r(){const{nextEl:a,prevEl:n}=e.navigation;if(e.params.loop)return l(n,!1),void l(a,!1);l(n,e.isBeginning&&!e.params.rewind),l(a,e.isEnd&&!e.params.rewind)}function o(a){a.preventDefault(),(!e.isBeginning||e.params.loop||e.params.rewind)&&(e.slidePrev(),t("navigationPrev"))}function d(a){a.preventDefault(),(!e.isEnd||e.params.loop||e.params.rewind)&&(e.slideNext(),t("navigationNext"))}function c(){const a=e.params.navigation;if(e.params.navigation=createElementIfNotDefined(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!a.nextEl&&!a.prevEl)return;let n=s(a.nextEl),i=s(a.prevEl);Object.assign(e.navigation,{nextEl:n,prevEl:i}),n=makeElementsArray(n),i=makeElementsArray(i);const t=(n,i)=>{n&&n.addEventListener("click","next"===i?d:o),!e.enabled&&n&&n.classList.add(...a.lockClass.split(" "))};n.forEach((a=>t(a,"next"))),i.forEach((a=>t(a,"prev")))}function m(){let{nextEl:a,prevEl:n}=e.navigation;a=makeElementsArray(a),n=makeElementsArray(n);const i=(a,n)=>{a.removeEventListener("click","next"===n?d:o),a.classList.remove(...e.params.navigation.disabledClass.split(" "))};a.forEach((a=>i(a,"next"))),n.forEach((a=>i(a,"prev")))}n({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null},i("init",(()=>{!1===e.params.navigation.enabled?p():(c(),r())})),i("toEdge fromEdge lock unlock",(()=>{r()})),i("destroy",(()=>{m()})),i("enable disable",(()=>{let{nextEl:a,prevEl:n}=e.navigation;a=makeElementsArray(a),n=makeElementsArray(n),e.enabled?r():[...a,...n].filter((a=>!!a)).forEach((a=>a.classList.add(e.params.navigation.lockClass)))})),i("click",((a,n)=>{let{nextEl:i,prevEl:s}=e.navigation;i=makeElementsArray(i),s=makeElementsArray(s);const l=n.target;let r=s.includes(l)||i.includes(l);if(e.isElement&&!r){const a=n.path||n.composedPath&&n.composedPath();a&&(r=a.find((a=>i.includes(a)||s.includes(a))))}if(e.params.navigation.hideOnClick&&!r){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===l||e.pagination.el.contains(l)))return;let a;i.length?a=i[0].classList.contains(e.params.navigation.hiddenClass):s.length&&(a=s[0].classList.contains(e.params.navigation.hiddenClass)),t(!0===a?"navigationShow":"navigationHide"),[...i,...s].filter((a=>!!a)).forEach((a=>a.classList.toggle(e.params.navigation.hiddenClass)))}}));const p=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),m()};Object.assign(e.navigation,{enable:()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),c(),r()},disable:p,update:r,init:c,destroy:m})}export{Navigation as default};
//# sourceMappingURL=navigation.min.mjs.map