import React, { useState, useEffect } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { useTheme } from '../../context/ThemeContext';

// Purple-themed scroll animations
export const PurpleScrollAnimation = ({ children, className = '', animationType = 'slideUp' }) => {
  const { isDark } = useTheme();
  const controls = useAnimation();
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: false // Allow re-triggering
  });

  const [isSelected, setIsSelected] = useState(false);

  useEffect(() => {
    if (inView) {
      setIsSelected(true);
      controls.start('visible');
    } else {
      setIsSelected(false);
      controls.start('hidden');
    }
  }, [controls, inView]);

  const animations = {
    slideUp: {
      hidden: { 
        opacity: 0, 
        y: 50,
        scale: 0.95,
        filter: 'blur(5px)'
      },
      visible: { 
        opacity: 1, 
        y: 0,
        scale: 1,
        filter: 'blur(0px)',
        transition: {
          duration: 0.8,
          ease: "easeOut"
        }
      }
    },
    scaleIn: {
      hidden: { 
        opacity: 0, 
        scale: 0.8,
        rotateY: -15
      },
      visible: { 
        opacity: 1, 
        scale: 1,
        rotateY: 0,
        transition: {
          duration: 0.6,
          ease: "backOut"
        }
      }
    },
    slideLeft: {
      hidden: { 
        opacity: 0, 
        x: -100,
        rotateX: 15
      },
      visible: { 
        opacity: 1, 
        x: 0,
        rotateX: 0,
        transition: {
          duration: 0.7,
          ease: "easeOut"
        }
      }
    }
  };

  const purpleGlow = isSelected ? {
    boxShadow: isDark 
      ? '0 0 30px rgba(147, 51, 234, 0.6), 0 0 60px rgba(147, 51, 234, 0.3), inset 0 0 20px rgba(147, 51, 234, 0.1)'
      : '0 0 25px rgba(147, 51, 234, 0.4), 0 0 50px rgba(147, 51, 234, 0.2), inset 0 0 15px rgba(147, 51, 234, 0.05)',
    border: `2px solid ${isDark ? 'rgba(147, 51, 234, 0.5)' : 'rgba(147, 51, 234, 0.3)'}`,
    background: isDark 
      ? 'linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(79, 70, 229, 0.05) 100%)'
      : 'linear-gradient(135deg, rgba(147, 51, 234, 0.05) 0%, rgba(79, 70, 229, 0.02) 100%)'
  } : {};

  return (
    <motion.div
      ref={ref}
      animate={controls}
      variants={animations[animationType]}
      className={`relative ${className}`}
      style={{
        ...purpleGlow,
        borderRadius: '12px',
        transition: 'all 0.5s ease'
      }}
    >
      {/* Purple particle effects when selected */}
      {isSelected && (
        <>
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-purple-500 rounded-full"
              style={{
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0, 1, 0],
                scale: [0, 1.5, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeInOut"
              }}
            />
          ))}
        </>
      )}
      
      {children}
    </motion.div>
  );
};

// Dropdown text animation component
export const DropdownText = ({ 
  title, 
  content, 
  triggerOnScroll = true, 
  className = '',
  delay = 0 
}) => {
  const { isDark } = useTheme();
  const [isExpanded, setIsExpanded] = useState(false);
  const [ref, inView] = useInView({
    threshold: 0.5,
    triggerOnce: false
  });

  useEffect(() => {
    if (triggerOnScroll && inView) {
      const timer = setTimeout(() => {
        setIsExpanded(true);
      }, delay);
      return () => clearTimeout(timer);
    } else if (triggerOnScroll && !inView) {
      setIsExpanded(false);
    }
  }, [inView, triggerOnScroll, delay]);

  const toggleExpanded = () => {
    if (!triggerOnScroll) {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <motion.div
      ref={ref}
      className={`relative overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Title with purple glow effect */}
      <motion.div
        className={`cursor-pointer p-4 rounded-lg transition-all duration-300 ${
          isExpanded 
            ? 'bg-gradient-to-r from-purple-500/20 to-indigo-500/20' 
            : 'hover:bg-purple-500/10'
        }`}
        onClick={toggleExpanded}
        whileHover={{ scale: 1.02 }}
        style={{
          boxShadow: isExpanded 
            ? isDark 
              ? '0 0 20px rgba(147, 51, 234, 0.4)' 
              : '0 0 15px rgba(147, 51, 234, 0.2)'
            : 'none'
        }}
      >
        <motion.h3
          className="text-xl font-semibold text-gray-900 dark:text-white flex items-center justify-between"
          animate={{
            color: isExpanded 
              ? isDark ? '#a855f7' : '#7c3aed'
              : isDark ? '#ffffff' : '#1f2937'
          }}
        >
          {title}
          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.3 }}
            className="text-purple-500"
          >
            ▼
          </motion.div>
        </motion.h3>
      </motion.div>

      {/* Dropdown content with purple animations */}
      <motion.div
        initial={{ height: 0, opacity: 0 }}
        animate={{
          height: isExpanded ? 'auto' : 0,
          opacity: isExpanded ? 1 : 0
        }}
        transition={{
          duration: 0.5,
          ease: "easeInOut"
        }}
        className="overflow-hidden"
      >
        <motion.div
          className="p-4 pt-0"
          initial={{ y: -20 }}
          animate={{ y: isExpanded ? 0 : -20 }}
          transition={{ duration: 0.4, delay: 0.1 }}
        >
          {/* Purple line separator */}
          <motion.div
            className="h-0.5 bg-gradient-to-r from-purple-500 to-indigo-500 mb-4"
            initial={{ scaleX: 0 }}
            animate={{ scaleX: isExpanded ? 1 : 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          />
          
          <motion.div
            className="text-gray-600 dark:text-gray-300 leading-relaxed"
            initial={{ opacity: 0, y: 10 }}
            animate={{ 
              opacity: isExpanded ? 1 : 0,
              y: isExpanded ? 0 : 10
            }}
            transition={{ duration: 0.4, delay: 0.3 }}
          >
            {content}
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Purple glow border when expanded */}
      {isExpanded && (
        <motion.div
          className="absolute inset-0 rounded-lg pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          style={{
            background: `linear-gradient(45deg, 
              transparent, 
              rgba(147, 51, 234, 0.1), 
              transparent, 
              rgba(79, 70, 229, 0.1), 
              transparent
            )`,
            backgroundSize: '200% 200%',
            animation: 'gradient-shift 3s ease infinite'
          }}
        />
      )}
    </motion.div>
  );
};

// Section highlight animation
export const SectionHighlight = ({ children, sectionId, className = '' }) => {
  const { isDark } = useTheme();
  const [isActive, setIsActive] = useState(false);
  const [ref, inView] = useInView({
    threshold: 0.4,
    triggerOnce: false
  });

  useEffect(() => {
    setIsActive(inView);
  }, [inView]);

  return (
    <motion.section
      ref={ref}
      id={sectionId}
      className={`relative ${className}`}
      animate={{
        backgroundColor: isActive 
          ? isDark 
            ? 'rgba(147, 51, 234, 0.05)' 
            : 'rgba(147, 51, 234, 0.02)'
          : 'transparent'
      }}
      transition={{ duration: 0.8 }}
    >
      {/* Purple border animation */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: isActive ? 1 : 0 }}
        transition={{ duration: 0.6 }}
        style={{
          border: `2px solid ${isActive ? 'rgba(147, 51, 234, 0.3)' : 'transparent'}`,
          borderRadius: '8px',
          boxShadow: isActive 
            ? isDark 
              ? '0 0 40px rgba(147, 51, 234, 0.3)' 
              : '0 0 30px rgba(147, 51, 234, 0.2)'
            : 'none'
        }}
      />

      {/* Animated corner accents */}
      {isActive && (
        <>
          {['top-left', 'top-right', 'bottom-left', 'bottom-right'].map((corner) => (
            <motion.div
              key={corner}
              className={`absolute w-6 h-6 border-purple-500 ${
                corner === 'top-left' ? 'top-2 left-2 border-t-2 border-l-2' :
                corner === 'top-right' ? 'top-2 right-2 border-t-2 border-r-2' :
                corner === 'bottom-left' ? 'bottom-2 left-2 border-b-2 border-l-2' :
                'bottom-2 right-2 border-b-2 border-r-2'
              }`}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.2 }}
            />
          ))}
        </>
      )}

      {children}
    </motion.section>
  );
};

// Purple card selection animation
export const SelectableCard = ({ children, className = '', onSelect }) => {
  const { isDark } = useTheme();
  const [isSelected, setIsSelected] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    setIsSelected(!isSelected);
    if (onSelect) onSelect(!isSelected);
  };

  return (
    <motion.div
      className={`relative cursor-pointer ${className}`}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      animate={{
        boxShadow: isSelected 
          ? isDark 
            ? '0 0 30px rgba(147, 51, 234, 0.6), 0 10px 40px rgba(147, 51, 234, 0.3)'
            : '0 0 25px rgba(147, 51, 234, 0.4), 0 8px 30px rgba(147, 51, 234, 0.2)'
          : isHovered
            ? isDark
              ? '0 0 20px rgba(147, 51, 234, 0.3)'
              : '0 0 15px rgba(147, 51, 234, 0.2)'
            : '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}
      style={{
        border: `2px solid ${
          isSelected 
            ? 'rgba(147, 51, 234, 0.6)' 
            : isHovered 
              ? 'rgba(147, 51, 234, 0.3)' 
              : 'transparent'
        }`,
        borderRadius: '12px',
        background: isSelected 
          ? isDark 
            ? 'linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(79, 70, 229, 0.05) 100%)'
            : 'linear-gradient(135deg, rgba(147, 51, 234, 0.05) 0%, rgba(79, 70, 229, 0.02) 100%)'
          : undefined
      }}
      transition={{ duration: 0.3 }}
    >
      {/* Selection indicator */}
      {isSelected && (
        <motion.div
          className="absolute top-2 right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 500, damping: 30 }}
        >
          <motion.div
            className="text-white text-xs"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            ✓
          </motion.div>
        </motion.div>
      )}

      {/* Purple sparkle effects when selected */}
      {isSelected && (
        <>
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-purple-400 rounded-full"
              style={{
                top: `${20 + Math.random() * 60}%`,
                left: `${20 + Math.random() * 60}%`,
              }}
              animate={{
                scale: [0, 1, 0],
                opacity: [0, 1, 0],
                rotate: [0, 180, 360]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeInOut"
              }}
            />
          ))}
        </>
      )}

      {children}
    </motion.div>
  );
};
