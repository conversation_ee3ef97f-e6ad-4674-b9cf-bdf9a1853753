import React from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import { ThemeProvider } from './context/ThemeContext';
import Navbar from './components/layout/Navbar';
import Footer from './components/layout/Footer';
import Home from './pages/Home';
import About from './pages/About';
import Services from './pages/Services';
import Portfolio from './pages/Portfolio';
import Contact from './pages/Contact';
import Blog from './pages/Blog';
import { ScrollToTop, FloatingContact, CursorFollower } from './components/ui/FloatingElements';
import { EnhancedPageTransition } from './components/ui/PageTransition';

function App() {
  const location = useLocation();

  return (
    <ThemeProvider>
      <div className="App">
        <CursorFollower />
        <Navbar />
        <main>
          <AnimatePresence mode="wait">
            <Routes location={location} key={location.pathname}>
              <Route path="/" element={
                <EnhancedPageTransition>
                  <Home />
                </EnhancedPageTransition>
              } />
              <Route path="/about" element={
                <EnhancedPageTransition>
                  <About />
                </EnhancedPageTransition>
              } />
              <Route path="/services" element={
                <EnhancedPageTransition>
                  <Services />
                </EnhancedPageTransition>
              } />
              <Route path="/portfolio" element={
                <EnhancedPageTransition>
                  <Portfolio />
                </EnhancedPageTransition>
              } />
              <Route path="/contact" element={
                <EnhancedPageTransition>
                  <Contact />
                </EnhancedPageTransition>
              } />
              <Route path="/blog" element={
                <EnhancedPageTransition>
                  <Blog />
                </EnhancedPageTransition>
              } />
            </Routes>
          </AnimatePresence>
        </main>
        <Footer />
        <ScrollToTop />
        <FloatingContact />
      </div>
    </ThemeProvider>
  );
}

export default App;
