import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import {
  Target,
  Eye,
  Heart,
  Users,
  Award,
  Linkedin,
  Twitter,
  Github,
  Instagram
} from 'lucide-react';

const About = () => {
  const [timelineRef, timelineInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  const values = [
    {
      icon: Target,
      title: 'Our Mission',
      description: 'To empower businesses with innovative digital solutions that drive growth and success in the modern world.',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Eye,
      title: 'Our Vision',
      description: 'To be the leading digital transformation partner, helping businesses thrive in the digital age.',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Heart,
      title: 'Our Values',
      description: 'Innovation, integrity, excellence, and customer-centricity guide everything we do.',
      color: 'from-green-500 to-emerald-500'
    },
  ];

  const team = [
    {
      name: '<PERSON> ',
      role: 'CEO & Founder',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
      bio: 'Visionary leader with 15+ years in tech industry.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    },
    {
      name: 'Mustafa',
      role: 'CTO',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
      bio: 'Tech expert specializing in scalable solutions.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#',
        instagram: '#'
      }
    },
    {
      name: 'Hammad Ul Rehman',
      role: 'Lead Designer',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
      bio: 'Creative designer with passion for user experience.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#',
        instagram: '#'
      }
    },
    {
      name: 'Ahmar Ashraf',
      role: 'Marketing Director',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
      bio: 'Strategic marketer driving brand growth.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#',
        instagram: '#'
      }
    },
  ];

  const timeline = [
    {
      year: '2014',
      title: 'Company Founded',
      description: 'Started as a small web development agency with big dreams.'
    },
    {
      year: '2016',
      title: 'First Major Client',
      description: 'Landed our first enterprise client and expanded our team.'
    },
    {
      year: '2018',
      title: 'Mobile Focus',
      description: 'Expanded into mobile app development and digital marketing.'
    },
    {
      year: '2020',
      title: 'Remote First',
      description: 'Transitioned to remote-first company, expanding globally.'
    },
    {
      year: '2022',
      title: 'AI Integration',
      description: 'Integrated AI and machine learning into our solutions.'
    },
    {
      year: '2024',
      title: 'Industry Leader',
      description: 'Recognized as a leading digital transformation partner.'
    },
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Modern Hero Section */}
      <section className="relative py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10" />
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl" />
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-pink-500/20 rounded-full blur-3xl" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-5xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-purple-500/20 border border-purple-500/30 mb-8"
            >
              <Users className="text-purple-400" size={16} />
              <span className="text-purple-300 font-medium">Meet Our Team</span>
            </motion.div>

            <h1 className="text-6xl md:text-8xl font-bold text-white mb-8 leading-tight">
              About
              <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
                Our Story
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed mb-12 max-w-4xl mx-auto">
              We're a passionate team of digital innovators dedicated to helping businesses
              succeed in the ever-evolving digital landscape with cutting-edge solutions.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission, Vision, Values */}
      <section className="py-20 bg-white dark:bg-slate-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Our Core Values & Philosophy
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
              At the heart of our company lies a commitment to excellence, innovation, and client success.
              We believe that great digital solutions are born from understanding our clients' unique challenges.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {values.map((value) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                whileHover={{
                  y: -10,
                  scale: 1.02,
                  boxShadow: "0 20px 40px rgba(147, 51, 234, 0.15)"
                }}
                transition={{ duration: 0.6 }}
                className="group text-center p-8 bg-white dark:bg-slate-800 rounded-2xl h-full shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600"
              >
                <motion.div
                  className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl mb-6 group-hover:shadow-lg"
                  whileHover={{
                    scale: 1.15,
                    rotate: 10,
                    boxShadow: "0 15px 35px rgba(147, 51, 234, 0.6)"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <value.icon className="text-white" size={24} />
                </motion.div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">
                  {value.title}
                </h3>
                <p className="text-gray-700 dark:text-gray-200 leading-relaxed font-medium">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-gray-50 dark:bg-slate-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Meet Our Team
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
              Our team is composed of passionate professionals who bring diverse expertise and innovative thinking to every project.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                whileHover={{
                  y: -8,
                  scale: 1.03,
                  boxShadow: "0 25px 50px rgba(147, 51, 234, 0.2)"
                }}
                transition={{ duration: 0.6 }}
                className="group bg-white dark:bg-slate-800 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600"
              >
                <div className="text-center">
                  <motion.img
                    src={member.image}
                    alt={member.name}
                    className="w-24 h-24 rounded-full mx-auto mb-4 object-cover border-4 border-gray-200 dark:border-slate-600 group-hover:border-purple-400 transition-colors duration-300"
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.3 }}
                  />
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">
                    {member.name}
                  </h3>
                  <p className="text-purple-600 dark:text-purple-400 font-bold mb-3 text-sm uppercase tracking-wide">
                    {member.role}
                  </p>
                  <p className="text-gray-700 dark:text-gray-200 text-sm mb-4 font-medium leading-relaxed">
                    {member.bio}
                  </p>
                  <div className="flex justify-center gap-3">
                    <motion.a
                      href={member.social.linkedin}
                      className="group relative p-2 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-400 hover:text-white transition-all duration-300 hover:bg-[#0077B5] hover:scale-110"
                      whileHover={{ y: -2 }}
                      title="LinkedIn"
                    >
                      <Linkedin size={18} />
                      <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        LinkedIn
                      </span>
                    </motion.a>
                    <motion.a
                      href={member.social.twitter}
                      className="group relative p-2 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-400 hover:text-white transition-all duration-300 hover:bg-[#1DA1F2] hover:scale-110"
                      whileHover={{ y: -2 }}
                      title="Twitter"
                    >
                      <Twitter size={18} />
                      <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        Twitter
                      </span>
                    </motion.a>
                    <motion.a
                      href={member.social.github}
                      className="group relative p-2 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-400 hover:text-white transition-all duration-300 hover:bg-[#333] hover:scale-110"
                      whileHover={{ y: -2 }}
                      title="GitHub"
                    >
                      <Github size={18} />
                      <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        GitHub
                      </span>
                    </motion.a>
                    <motion.a
                      href={member.social.instagram}
                      className="group relative p-2 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-400 hover:text-white transition-all duration-300 hover:bg-gradient-to-r hover:from-[#833AB4] hover:via-[#FD1D1D] hover:to-[#F77737] hover:scale-110"
                      whileHover={{ y: -2 }}
                      title="Instagram"
                    >
                      <Instagram size={18} />
                      <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        Instagram
                      </span>
                    </motion.a>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section ref={timelineRef} className="py-20 bg-white dark:bg-slate-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={timelineInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Our Journey
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              From humble beginnings to industry leadership, here's how we've grown over the years.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            {timeline.map((item, index) => (
              <motion.div
                key={item.year}
                initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                animate={timelineInView ? { opacity: 1, x: 0 } : {}}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`flex items-center mb-12 ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
              >
                <div className={`flex-1 ${index % 2 === 0 ? 'text-right pr-8' : 'text-left pl-8'}`}>
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-lg font-bold px-4 py-2 rounded-full inline-block mb-4">
                    {item.year}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {item.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {item.description}
                  </p>
                </div>
                <div className="w-4 h-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex-shrink-0 mx-4"></div>
                <div className="flex-1"></div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
