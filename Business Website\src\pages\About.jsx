import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import {
  Target,
  Eye,
  Heart,
  Users,
  Award,
  Linkedin,
  Twitter,
  Github
} from 'lucide-react';
import AnimatedBackground from '../components/ui/AnimatedBackground';
import { AnimatedHeading, AnimatedParagraph } from '../components/ui/AnimatedText';
import AnimatedCard, { TeamCard } from '../components/ui/AnimatedCard';
import {
  PurpleScrollAnimation,
  DropdownText,
  SectionHighlight,
  SelectableCard
} from '../components/ui/ScrollAnimations';

const About = () => {
  const [heroRef, heroInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [missionRef, missionInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [teamRef, teamInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [timelineRef, timelineInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  const values = [
    {
      icon: Target,
      title: 'Our Mission',
      description: 'To empower businesses with innovative digital solutions that drive growth and success in the modern world.',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Eye,
      title: 'Our Vision',
      description: 'To be the leading digital transformation partner, helping businesses thrive in the digital age.',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Heart,
      title: 'Our Values',
      description: 'Innovation, integrity, excellence, and customer-centricity guide everything we do.',
      color: 'from-green-500 to-emerald-500'
    },
  ];

  const team = [
    {
      name: 'Muhammad Aqeel',
      role: 'CEO & Founder',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
      bio: 'Visionary leader with 15+ years in tech industry.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    },
    {
      name: 'Mustafa',
      role: 'CTO',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
      bio: 'Tech expert specializing in scalable solutions.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    },
    {
      name: 'Hammad Ul Rehman',
      role: 'Lead Designer',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
      bio: 'Creative designer with passion for user experience.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    },
    {
      name: 'Ahmar Ashraf',
      role: 'Marketing Director',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
      bio: 'Strategic marketer driving brand growth.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    },
  ];

  const timeline = [
    {
      year: '2014',
      title: 'Company Founded',
      description: 'Started as a small web development agency with big dreams.'
    },
    {
      year: '2016',
      title: 'First Major Client',
      description: 'Landed our first enterprise client and expanded our team.'
    },
    {
      year: '2018',
      title: 'Mobile Focus',
      description: 'Expanded into mobile app development and digital marketing.'
    },
    {
      year: '2020',
      title: 'Remote First',
      description: 'Transitioned to remote-first company, expanding globally.'
    },
    {
      year: '2022',
      title: 'AI Integration',
      description: 'Integrated AI and machine learning into our solutions.'
    },
    {
      year: '2024',
      title: 'Industry Leader',
      description: 'Recognized as a leading digital transformation partner.'
    },
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <AnimatedBackground variant="hero" className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <AnimatedHeading
              level={1}
              className="text-5xl md:text-6xl mb-6"
              variant="slideInUp"
              stagger={true}
            >
              About Our Story
            </AnimatedHeading>
            <AnimatedParagraph
              className="text-xl md:text-2xl leading-relaxed"
              variant="fadeInUp"
              delay={0.4}
            >
              We're a passionate team of digital innovators dedicated to helping businesses
              succeed in the ever-evolving digital landscape.
            </AnimatedParagraph>
          </motion.div>
        </div>
      </AnimatedBackground>

      {/* Mission, Vision, Values */}
      <SectionHighlight sectionId="mission" className="py-20 bg-white dark:bg-slate-800">
        <div className="container mx-auto px-4">
          {/* Dropdown introduction */}
          <div className="max-w-4xl mx-auto mb-16">
            <DropdownText
              title="Our Core Values & Philosophy"
              content="At the heart of our company lies a commitment to excellence, innovation, and client success. We believe that great digital solutions are born from understanding our clients' unique challenges and crafting tailored approaches that not only meet but exceed expectations. Our values guide every decision we make and every solution we create."
              triggerOnScroll={true}
              delay={300}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <PurpleScrollAnimation
                key={value.title}
                animationType="slideUp"
                className="h-full"
              >
                <SelectableCard className="text-center p-8 bg-gray-50 dark:bg-slate-700 rounded-2xl h-full">
                  <motion.div
                    className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl mb-6"
                    whileHover={{
                      scale: 1.1,
                      rotate: 5,
                      boxShadow: "0 10px 30px rgba(147, 51, 234, 0.4)"
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <value.icon className="text-white" size={24} />
                  </motion.div>
                  <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                    {value.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {value.description}
                  </p>
                </SelectableCard>
              </PurpleScrollAnimation>
            ))}
          </div>
        </div>
      </SectionHighlight>

      {/* Team Section */}
      <SectionHighlight sectionId="team" className="py-20 bg-gray-50 dark:bg-slate-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={teamInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Meet Our Team
            </h2>

            {/* Dropdown text for team introduction */}
            <div className="max-w-4xl mx-auto mb-8">
              <DropdownText
                title="The Minds Behind Our Success"
                content="Our team is composed of passionate professionals who bring diverse expertise and innovative thinking to every project. From seasoned developers and creative designers to strategic marketers and visionary leaders, each team member contributes unique skills that collectively drive our mission forward. We believe in collaboration, continuous learning, and pushing the boundaries of what's possible in digital innovation."
                triggerOnScroll={true}
                delay={400}
              />
            </div>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <PurpleScrollAnimation
                key={member.name}
                animationType="scaleIn"
                className="h-full"
              >
                <SelectableCard className="h-full">
                  <TeamCard
                    image={member.image}
                    name={member.name}
                    role={member.role}
                    bio={member.bio}
                    social={member.social}
                    className="h-full border-0 shadow-none bg-transparent"
                  />
                </SelectableCard>
              </PurpleScrollAnimation>
            ))}
          </div>
        </div>
      </SectionHighlight>

      {/* Timeline Section */}
      <section ref={timelineRef} className="py-20 bg-white dark:bg-slate-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={timelineInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Our Journey
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              From humble beginnings to industry leadership, here's how we've grown over the years.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            {timeline.map((item, index) => (
              <motion.div
                key={item.year}
                initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                animate={timelineInView ? { opacity: 1, x: 0 } : {}}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`flex items-center mb-12 ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
              >
                <div className={`flex-1 ${index % 2 === 0 ? 'text-right pr-8' : 'text-left pl-8'}`}>
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-lg font-bold px-4 py-2 rounded-full inline-block mb-4">
                    {item.year}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {item.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {item.description}
                  </p>
                </div>
                <div className="w-4 h-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex-shrink-0 mx-4"></div>
                <div className="flex-1"></div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
