import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  ArrowRight, 
  Users, 
  Award, 
  Clock, 
  Star,
  Code,
  Smartphone,
  TrendingUp,
  Shield
} from 'lucide-react';
import { useInView } from 'react-intersection-observer';
import Testimonials from '../components/sections/Testimonials';
import AnimatedBackground from '../components/ui/AnimatedBackground';
import { AnimatedHeading, AnimatedParagraph, TypewriterText } from '../components/ui/AnimatedText';
import AnimatedCard, { ServiceCard } from '../components/ui/AnimatedCard';
import { 
  PurpleScrollAnimation, 
  DropdownText, 
  SectionHighlight, 
  SelectableCard 
} from '../components/ui/ScrollAnimations';

const Home = () => {
  const [heroRef, heroInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [statsRef, statsInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [servicesRef, servicesInView] = useInView({ threshold: 0.3, triggerOnce: true });

  const stats = [
    { number: '500+', label: 'Projects Completed', icon: Award },
    { number: '98%', label: 'Client Satisfaction', icon: Star },
    { number: '24/7', label: 'Support Available', icon: Clock },
    { number: '50+', label: 'Team Members', icon: Users }
  ];

  const services = [
    {
      icon: Code,
      title: 'Web Development',
      description: 'Custom websites and web applications built with modern technologies.',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Smartphone,
      title: 'Mobile Apps',
      description: 'Native and cross-platform mobile applications for iOS and Android.',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: TrendingUp,
      title: 'Digital Marketing',
      description: 'SEO, social media marketing, and digital advertising strategies.',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Shield,
      title: 'Consulting',
      description: 'Strategic technology consulting and digital transformation.',
      color: 'from-orange-500 to-red-500'
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedBackground variant="hero" className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              ref={heroRef}
              initial={{ opacity: 0, y: 50 }}
              animate={heroInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 1, ease: "easeOut" }}
            >
              <TypewriterText 
                text="Building Digital Excellence"
                className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                speed={100}
              />
              
              <AnimatedParagraph 
                className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto"
                variant="fadeInUp"
                delay={0.5}
              >
                We create stunning websites, powerful applications, and digital solutions that drive your business forward.
              </AnimatedParagraph>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={heroInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: 0.8 }}
                className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
              >
                <Link
                  to="/contact"
                  className="btn btn-primary text-lg px-8 py-4 group"
                >
                  Get Started Today
                  <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={20} />
                </Link>
                
                <Link
                  to="/portfolio"
                  className="btn btn-secondary text-lg px-8 py-4"
                >
                  View Our Work
                </Link>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={heroInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: 1 }}
                className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-gray-600 dark:text-gray-300"
              >
                <div className="flex items-center justify-center gap-2">
                  <Users className="text-blue-500" size={16} />
                  <span>500+ Happy Clients</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <Award className="text-blue-500" size={16} />
                  <span>Award-winning team</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <Shield className="text-green-500" size={16} />
                  <span>100% satisfaction guarantee</span>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </AnimatedBackground>

      {/* Stats Section */}
      <SectionHighlight sectionId="stats" className="py-20 bg-white dark:bg-slate-800">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <PurpleScrollAnimation
                key={stat.label}
                animationType="scaleIn"
                className="text-center"
              >
                <SelectableCard className="p-6 bg-white dark:bg-slate-700 rounded-xl">
                  <motion.div 
                    className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full mb-4 mx-auto"
                    whileHover={{ 
                      scale: 1.2, 
                      rotate: 360,
                      boxShadow: "0 10px 30px rgba(147, 51, 234, 0.6)"
                    }}
                    transition={{ duration: 0.5 }}
                  >
                    <stat.icon className="text-white" size={24} />
                  </motion.div>
                  
                  <motion.div 
                    className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2"
                    initial={{ scale: 0 }}
                    animate={statsInView ? { scale: 1 } : {}}
                    transition={{ 
                      duration: 0.8, 
                      delay: 0.3 + (index * 0.1),
                      type: "spring",
                      stiffness: 100
                    }}
                  >
                    {stat.number}
                  </motion.div>
                  
                  <motion.div 
                    className="text-gray-600 dark:text-gray-300"
                    initial={{ opacity: 0, y: 10 }}
                    animate={statsInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.6, delay: 0.5 + (index * 0.1) }}
                  >
                    {stat.label}
                  </motion.div>
                </SelectableCard>
              </PurpleScrollAnimation>
            ))}
          </div>
        </div>
      </SectionHighlight>

      {/* Services Preview */}
      <SectionHighlight sectionId="services" className="py-20 bg-gray-50 dark:bg-slate-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={servicesInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <AnimatedHeading 
              level={2} 
              className="text-4xl md:text-5xl mb-6"
              variant="scaleIn"
              delay={0.2}
            >
              What We Do
            </AnimatedHeading>
            
            <div className="max-w-4xl mx-auto mb-8">
              <DropdownText
                title="Discover Our Comprehensive Digital Solutions"
                content="We specialize in creating cutting-edge digital experiences that transform businesses. From responsive web development to innovative mobile applications, our team combines creativity with technical expertise to deliver solutions that drive growth and engagement."
                triggerOnScroll={true}
                delay={500}
              />
            </div>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service, index) => (
              <PurpleScrollAnimation
                key={service.title}
                animationType="slideUp"
                className="h-full"
              >
                <SelectableCard className="h-full p-6 bg-white dark:bg-slate-800 rounded-2xl">
                  <motion.div 
                    className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl mb-6"
                    whileHover={{ 
                      scale: 1.15, 
                      rotate: 10,
                      boxShadow: "0 15px 35px rgba(147, 51, 234, 0.4)"
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <service.icon className="text-white" size={24} />
                  </motion.div>
                  
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    {service.title}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {service.description}
                  </p>
                </SelectableCard>
              </PurpleScrollAnimation>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-center mt-16"
          >
            <Link
              to="/services"
              className="btn btn-primary text-lg px-8 py-4 group"
            >
              View All Services
              <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={20} />
            </Link>
          </motion.div>
        </div>
      </SectionHighlight>

      {/* Testimonials Section */}
      <Testimonials />

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Ready to Start Your Project?
            </h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
              Let's work together to bring your vision to life with cutting-edge technology and creative excellence.
            </p>
            <Link
              to="/contact"
              className="btn bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-4 group"
            >
              Get In Touch
              <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={20} />
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
