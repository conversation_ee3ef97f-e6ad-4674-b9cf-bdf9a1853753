import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON> } from 'react-router-dom';
import {
  <PERSON>Right,
  Star,
  Zap,
  Shield,
  Globe,
  Users,
  TrendingUp,
  Award,
  CheckCircle,
  Sparkles
} from 'lucide-react';

const Home = () => {
  return (
    <div className="min-h-screen">
      {/* Modern Hero Section */}
      <section className="relative py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden min-h-screen flex items-center">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10" />
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-pink-500/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-6xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-purple-500/20 border border-purple-500/30 mb-8"
            >
              <Sparkles className="text-purple-400" size={16} />
              <span className="text-purple-300 font-medium">Building Digital Excellence</span>
            </motion.div>

            <h1 className="text-6xl md:text-8xl lg:text-9xl font-bold text-white mb-8 leading-tight">
              Transform Your
              <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
                Digital Future
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed mb-12 max-w-4xl mx-auto">
              We create stunning websites, powerful applications, and cutting-edge digital solutions
              that drive your business forward and captivate your audience.
            </p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-6 justify-center mb-16"
            >
              <Link
                to="/contact"
                className="px-10 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full font-bold text-lg hover:scale-105 transition-transform duration-300 hover:shadow-2xl hover:shadow-purple-500/25 flex items-center justify-center"
              >
                Get Started Today
                <ArrowRight className="ml-2" size={20} />
              </Link>

              <Link
                to="/portfolio"
                className="px-10 py-4 border-2 border-purple-400 text-purple-300 rounded-full font-bold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center justify-center"
              >
                View Our Work
                <Globe className="ml-2" size={20} />
              </Link>
          </div>
        </div>
      </section>

      {/* Simple Stats Section */}
      <section className="py-20 bg-white dark:bg-slate-800">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <motion.div
              className="group p-8 bg-gray-50 dark:bg-slate-700 rounded-2xl hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-slate-600 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -5, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="text-4xl font-bold text-purple-600 mb-3 group-hover:text-purple-700 transition-colors">500+</div>
              <div className="text-gray-700 dark:text-gray-200 font-medium">Projects Completed</div>
            </motion.div>
            <motion.div
              className="group p-8 bg-gray-50 dark:bg-slate-700 rounded-2xl hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-slate-600 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -5, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="text-4xl font-bold text-purple-600 mb-3 group-hover:text-purple-700 transition-colors">98%</div>
              <div className="text-gray-700 dark:text-gray-200 font-medium">Client Satisfaction</div>
            </motion.div>
            <motion.div
              className="group p-8 bg-gray-50 dark:bg-slate-700 rounded-2xl hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-slate-600 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -5, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="text-4xl font-bold text-purple-600 mb-3 group-hover:text-purple-700 transition-colors">24/7</div>
              <div className="text-gray-700 dark:text-gray-200 font-medium">Support Available</div>
            </motion.div>
            <motion.div
              className="group p-8 bg-gray-50 dark:bg-slate-700 rounded-2xl hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-slate-600 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -5, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="text-4xl font-bold text-purple-600 mb-3 group-hover:text-purple-700 transition-colors">50+</div>
              <div className="text-gray-700 dark:text-gray-200 font-medium">Team Members</div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Simple Services Section */}
      <section className="py-20 bg-gray-50 dark:bg-slate-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              What We Do
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              We specialize in creating cutting-edge digital experiences that transform businesses.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <motion.div
              className="group p-8 bg-white dark:bg-slate-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -8, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <motion.div
                className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl mb-6 flex items-center justify-center group-hover:shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-white text-2xl">💻</span>
              </motion.div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                Web Development
              </h3>
              <p className="text-gray-700 dark:text-gray-200 font-medium leading-relaxed">
                Custom websites and web applications built with modern technologies.
              </p>
            </motion.div>

            <motion.div
              className="group p-8 bg-white dark:bg-slate-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -8, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <motion.div
                className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl mb-6 flex items-center justify-center group-hover:shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-white text-2xl">📱</span>
              </motion.div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                Mobile Apps
              </h3>
              <p className="text-gray-700 dark:text-gray-200 font-medium leading-relaxed">
                Native and cross-platform mobile applications for iOS and Android.
              </p>
            </motion.div>

            <motion.div
              className="group p-8 bg-white dark:bg-slate-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -8, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <motion.div
                className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl mb-6 flex items-center justify-center group-hover:shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-white text-2xl">📈</span>
              </motion.div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                Digital Marketing
              </h3>
              <p className="text-gray-700 dark:text-gray-200 font-medium leading-relaxed">
                SEO, social media marketing, and digital advertising strategies.
              </p>
            </motion.div>

            <motion.div
              className="group p-8 bg-white dark:bg-slate-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -8, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <motion.div
                className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl mb-6 flex items-center justify-center group-hover:shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-white text-2xl">🛡️</span>
              </motion.div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                Consulting
              </h3>
              <p className="text-gray-700 dark:text-gray-200 font-medium leading-relaxed">
                Strategic technology consulting and digital transformation.
              </p>
            </motion.div>
          </div>

          <div className="text-center mt-16">
            <Link
              to="/services"
              className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-full font-semibold text-lg hover:scale-105 transition-transform duration-300"
            >
              View All Services
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Start Your Project?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Let's work together to bring your vision to life with cutting-edge technology and creative excellence.
          </p>
          <Link
            to="/contact"
            className="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 rounded-full font-semibold text-lg hover:bg-gray-100 transition-colors duration-300"
          >
            Get In Touch
          </Link>
        </div>
      </section>
    </div>
  );
};

export default Home;
