import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const Home = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 dark:from-slate-900 dark:to-slate-800">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Building Digital Excellence
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            We create stunning websites, powerful applications, and digital solutions that drive your business forward.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold text-lg hover:scale-105 transition-transform duration-300"
            >
              Get Started Today
            </Link>
            
            <Link
              to="/portfolio"
              className="inline-flex items-center justify-center px-8 py-4 border-2 border-gray-300 dark:border-gray-600 rounded-full font-semibold text-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-300"
            >
              View Our Work
            </Link>
          </div>
        </div>
      </section>

      {/* Simple Stats Section */}
      <section className="py-20 bg-white dark:bg-slate-800">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <motion.div
              className="group p-8 bg-gray-50 dark:bg-slate-700 rounded-2xl hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-slate-600 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -5, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="text-4xl font-bold text-purple-600 mb-3 group-hover:text-purple-700 transition-colors">500+</div>
              <div className="text-gray-700 dark:text-gray-200 font-medium">Projects Completed</div>
            </motion.div>
            <motion.div
              className="group p-8 bg-gray-50 dark:bg-slate-700 rounded-2xl hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-slate-600 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -5, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="text-4xl font-bold text-purple-600 mb-3 group-hover:text-purple-700 transition-colors">98%</div>
              <div className="text-gray-700 dark:text-gray-200 font-medium">Client Satisfaction</div>
            </motion.div>
            <motion.div
              className="group p-8 bg-gray-50 dark:bg-slate-700 rounded-2xl hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-slate-600 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -5, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="text-4xl font-bold text-purple-600 mb-3 group-hover:text-purple-700 transition-colors">24/7</div>
              <div className="text-gray-700 dark:text-gray-200 font-medium">Support Available</div>
            </motion.div>
            <motion.div
              className="group p-8 bg-gray-50 dark:bg-slate-700 rounded-2xl hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-slate-600 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -5, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="text-4xl font-bold text-purple-600 mb-3 group-hover:text-purple-700 transition-colors">50+</div>
              <div className="text-gray-700 dark:text-gray-200 font-medium">Team Members</div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Simple Services Section */}
      <section className="py-20 bg-gray-50 dark:bg-slate-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              What We Do
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              We specialize in creating cutting-edge digital experiences that transform businesses.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <motion.div
              className="group p-8 bg-white dark:bg-slate-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -8, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <motion.div
                className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl mb-6 flex items-center justify-center group-hover:shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-white text-2xl">💻</span>
              </motion.div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                Web Development
              </h3>
              <p className="text-gray-700 dark:text-gray-200 font-medium leading-relaxed">
                Custom websites and web applications built with modern technologies.
              </p>
            </motion.div>

            <motion.div
              className="group p-8 bg-white dark:bg-slate-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -8, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <motion.div
                className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl mb-6 flex items-center justify-center group-hover:shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-white text-2xl">📱</span>
              </motion.div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                Mobile Apps
              </h3>
              <p className="text-gray-700 dark:text-gray-200 font-medium leading-relaxed">
                Native and cross-platform mobile applications for iOS and Android.
              </p>
            </motion.div>

            <motion.div
              className="group p-8 bg-white dark:bg-slate-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -8, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <motion.div
                className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl mb-6 flex items-center justify-center group-hover:shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-white text-2xl">📈</span>
              </motion.div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                Digital Marketing
              </h3>
              <p className="text-gray-700 dark:text-gray-200 font-medium leading-relaxed">
                SEO, social media marketing, and digital advertising strategies.
              </p>
            </motion.div>

            <motion.div
              className="group p-8 bg-white dark:bg-slate-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600"
              whileHover={{ y: -8, scale: 1.02 }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <motion.div
                className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl mb-6 flex items-center justify-center group-hover:shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-white text-2xl">🛡️</span>
              </motion.div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                Consulting
              </h3>
              <p className="text-gray-700 dark:text-gray-200 font-medium leading-relaxed">
                Strategic technology consulting and digital transformation.
              </p>
            </motion.div>
          </div>

          <div className="text-center mt-16">
            <Link
              to="/services"
              className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-full font-semibold text-lg hover:scale-105 transition-transform duration-300"
            >
              View All Services
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Start Your Project?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Let's work together to bring your vision to life with cutting-edge technology and creative excellence.
          </p>
          <Link
            to="/contact"
            className="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 rounded-full font-semibold text-lg hover:bg-gray-100 transition-colors duration-300"
          >
            Get In Touch
          </Link>
        </div>
      </section>
    </div>
  );
};

export default Home;
