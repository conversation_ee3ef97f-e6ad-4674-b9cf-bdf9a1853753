import{e as effectInit}from"../shared/effect-init.min.mjs";import{c as createElement,p as getRotateFix}from"../shared/utils.min.mjs";function EffectCube(e){let{swiper:t,extendParams:s,on:a}=e;s({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}});const r=(e,t,s)=>{let a=s?e.querySelector(".swiper-slide-shadow-left"):e.querySelector(".swiper-slide-shadow-top"),r=s?e.querySelector(".swiper-slide-shadow-right"):e.querySelector(".swiper-slide-shadow-bottom");a||(a=createElement("div",("swiper-slide-shadow-cube swiper-slide-shadow-"+(s?"left":"top")).split(" ")),e.append(a)),r||(r=createElement("div",("swiper-slide-shadow-cube swiper-slide-shadow-"+(s?"right":"bottom")).split(" ")),e.append(r)),a&&(a.style.opacity=Math.max(-t,0)),r&&(r.style.opacity=Math.max(t,0))};effectInit({effect:"cube",swiper:t,on:a,setTranslate:()=>{const{el:e,wrapperEl:s,slides:a,width:o,height:i,rtlTranslate:l,size:d,browser:p}=t,n=getRotateFix(t),c=t.params.cubeEffect,w=t.isHorizontal(),h=t.virtual&&t.params.virtual.enabled;let f,m=0;c.shadow&&(w?(f=t.wrapperEl.querySelector(".swiper-cube-shadow"),f||(f=createElement("div","swiper-cube-shadow"),t.wrapperEl.append(f)),f.style.height=`${o}px`):(f=e.querySelector(".swiper-cube-shadow"),f||(f=createElement("div","swiper-cube-shadow"),e.append(f))));for(let e=0;e<a.length;e+=1){const t=a[e];let s=e;h&&(s=parseInt(t.getAttribute("data-swiper-slide-index"),10));let o=90*s,i=Math.floor(o/360);l&&(o=-o,i=Math.floor(-o/360));const p=Math.max(Math.min(t.progress,1),-1);let f=0,u=0,b=0;s%4==0?(f=4*-i*d,b=0):(s-1)%4==0?(f=0,b=4*-i*d):(s-2)%4==0?(f=d+4*i*d,b=d):(s-3)%4==0&&(f=-d,b=3*d+4*d*i),l&&(f=-f),w||(u=f,f=0);const x=`rotateX(${n(w?0:-o)}deg) rotateY(${n(w?o:0)}deg) translate3d(${f}px, ${u}px, ${b}px)`;p<=1&&p>-1&&(m=90*s+90*p,l&&(m=90*-s-90*p)),t.style.transform=x,c.slideShadows&&r(t,p,w)}if(s.style.transformOrigin=`50% 50% -${d/2}px`,s.style["-webkit-transform-origin"]=`50% 50% -${d/2}px`,c.shadow)if(w)f.style.transform=`translate3d(0px, ${o/2+c.shadowOffset}px, ${-o/2}px) rotateX(89.99deg) rotateZ(0deg) scale(${c.shadowScale})`;else{const e=Math.abs(m)-90*Math.floor(Math.abs(m)/90),t=1.5-(Math.sin(2*e*Math.PI/360)/2+Math.cos(2*e*Math.PI/360)/2),s=c.shadowScale,a=c.shadowScale/t,r=c.shadowOffset;f.style.transform=`scale3d(${s}, 1, ${a}) translate3d(0px, ${i/2+r}px, ${-i/2/a}px) rotateX(-89.99deg)`}const u=(p.isSafari||p.isWebView)&&p.needPerspectiveFix?-d/2:0;s.style.transform=`translate3d(0px,0,${u}px) rotateX(${n(t.isHorizontal()?0:m)}deg) rotateY(${n(t.isHorizontal()?-m:0)}deg)`,s.style.setProperty("--swiper-cube-translate-z",`${u}px`)},setTransition:e=>{const{el:s,slides:a}=t;if(a.forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((t=>{t.style.transitionDuration=`${e}ms`}))})),t.params.cubeEffect.shadow&&!t.isHorizontal()){const t=s.querySelector(".swiper-cube-shadow");t&&(t.style.transitionDuration=`${e}ms`)}},recreateShadows:()=>{const e=t.isHorizontal();t.slides.forEach((t=>{const s=Math.max(Math.min(t.progress,1),-1);r(t,s,e)}))},getEffectParams:()=>t.params.cubeEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0})})}export{EffectCube as default};
//# sourceMappingURL=effect-cube.min.mjs.map